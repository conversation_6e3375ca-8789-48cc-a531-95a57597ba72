using AutoMapper;
using BCVP.Net8.IService;
using BCVP.Net8.Model.Vo;
using BCVP.Net8.Repository;
using BCVP.Net8.Repository.Base;
using SqlSugar;

namespace BCVP.Net8.Service
{
    public class BaseService<TEntity, TVo> : IBaseService<TEntity, TVo> where TEntity : class, new()
    {
        private readonly IMapper _mapper;
        private readonly IBaseRepository<TEntity> _baseRepository;
        public ISqlSugarClient Db => _baseRepository.Db;

        public BaseService(IMapper mapper, IBaseRepository<TEntity> baseRepository)
        {
            _mapper = mapper;
            _baseRepository = baseRepository;
        }

        public async Task<List<TVo>> Query()
        {
            var entities = await _baseRepository.Query();

            var data = _mapper.Map<List<TVo>>(entities);

            return data;
        }

        /// <summary>
        /// 写入实体数据
        /// </summary>
        /// <param name="entity">博文实体类</param>
        /// <returns></returns>
        public async Task<long> Add(TEntity entity)
        {
            return await _baseRepository.Add(entity);
        }
    }
}
