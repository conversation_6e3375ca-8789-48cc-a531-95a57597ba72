using Autofac;
using Autofac.Extensions.DependencyInjection;
using BCVP.Net8.Common;
using BCVP.Net8.Extensions;
using BCVP.Net8.F.Extensions;
using BCVP.Net8.IService;
using BCVP.Net8.Repository.Base;
using BCVP.Net8.Service;
using Microsoft.AspNetCore.Mvc.Controllers;
using Microsoft.Extensions.DependencyInjection.Extensions;
using BCVP.Net8.Common.Option;
using BCVP.Net8.Common.Core;
using BCVP.Net8.Extensions.ServiceExtensions;

var builder = WebApplication.CreateBuilder(args);
//Autofac
builder.Host.UseServiceProviderFactory(new AutofacServiceProviderFactory())
    .ConfigureContainer<ContainerBuilder>(builder =>
    {
        builder.RegisterModule<AutofacModuleRegister>();//����д��ע�������õ���������
        builder.RegisterModule<AutofacPropertityModuleReg>();//����ע��
    }).ConfigureAppConfiguration((hostingContext, config) =>
    {
        hostingContext.Configuration.ConfigureApplication();//��ȡ����
    });

builder.ConfigureApplication();//��ȡwebhost��������

// Add services to the container.

//AutoFac ����ע�� ��������������Ĭ��û�п�������Ҫ�ֶ�����
builder.Services.Replace(ServiceDescriptor.Transient<IControllerActivator, ServiceBasedControllerActivator>());
builder.Services.AddControllers();

// Learn more about configuring Swagger/OpenAPI at https://aka.ms/aspnetcore/swashbuckle
builder.Services.AddEndpointsApiExplorer();
builder.Services.AddSwaggerGen();

//ԭ������ע��
//builder.Services.AddScoped(typeof(IBaseRepository<>), typeof(BaseRepository<>));
//builder.Services.AddScoped(typeof(IBaseService<,>), typeof(BaseService<,>));

//AutoMapper
builder.Services.AddAutoMapper(typeof(AutoMapperConfig));
AutoMapperConfig.RegisterMappings();

// ����
builder.Services.AddSingleton(new AppSettings(builder.Configuration));
builder.Services.AddAllOptionRegister();

// ����
builder.Services.AddCacheSetup();

//SqlSugar
builder.Services.AddSqlsugarSetup();

var app = builder.Build();
app.ConfigureApplication();//��ȡService
app.UseApplicationSetup();

// Configure the HTTP request pipeline.
if (app.Environment.IsDevelopment())
{
    app.UseSwagger();
    app.UseSwaggerUI();
}

app.UseHttpsRedirection();

app.UseAuthorization();

app.MapControllers();

app.Run();
