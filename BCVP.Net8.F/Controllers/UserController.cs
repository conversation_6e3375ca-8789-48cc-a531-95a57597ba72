using BCVP.Net8.Common;
using BCVP.Net8.Common.Caches;
using BCVP.Net8.Common.Core;
using BCVP.Net8.Common.Option;
using BCVP.Net8.IService;
using BCVP.Net8.Model;
using BCVP.Net8.Model.Vo;
using BCVP.Net8.Service;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Options;
using Newtonsoft.Json;

namespace BCVP.Net8.F.Controllers
{
    [ApiController]
    [Route("[controller]/[action]")]
    public class UserController : ControllerBase
    {
        //���캯��ע��
        private readonly IBaseService<Role, RoleVo> _baseService;
        private readonly IOptions<RedisOptions> _redisOptions;
        private readonly ICaching _caching;

        //����ע��
        public IBaseService<Role, RoleVo> _baseService1 { get; set; }

        public UserController(IBaseService<Role, RoleVo> baseService, IOptions<RedisOptions> reidsOptions, ICaching caching)
        {
            _baseService = baseService;
            _redisOptions = reidsOptions;
            _caching = caching;
        }

        //[HttpGet]
        //public async Task<List<UserVo>> GetUser()
        //{
        //    var userService = new UserService();
        //    var data = await userService.Query();
        //    return data;
        //}

        [HttpGet]
        public async Task<object> GetRole()
        {
            var data = await _baseService.Query();
            //var data = await _baseService1.Query();

            //��װAppsetting�������ȡ����
            var redisEnable = AppSettings.app(new string[] { "Redis", "Enable" });
            var redisConnectionString = AppSettings.GetValue("Redis:ConnectionString");
            Console.WriteLine($"Enable: {redisEnable} ,  ConnectionString: {redisConnectionString}");

            //ʹ��IOption��ʽ��ȡ����ѡ��
            var redis = _redisOptions.Value;
            Console.WriteLine(JsonConvert.SerializeObject(redis));

            return data;
        }

        /// <summary>
        /// ����λ��
        /// ��ʹ������ע��ķ�ʽֱ�ӻ�ȡ����ʵ��
        /// </summary>
        /// <returns></returns>
        [HttpGet]
        public async Task<object> GetOne()
        {
            var roleServiceObjNew = App.GetService<IBaseService<Role, RoleVo>>(false);

            var data = await _baseService.Query();

            Console.WriteLine(JsonConvert.SerializeObject(data));

            var redisOptions = App.GetOptions<RedisOptions>();

            Console.WriteLine(JsonConvert.SerializeObject(redisOptions));

            return data;
        }

        [HttpGet]
        public async void GetCatch()
        {
            var cacheKey = "cache-key";
            List<string> cacheKeys = await _caching.GetAllCacheKeysAsync();
            await Console.Out.WriteLineAsync("ȫ��keys -->" + JsonConvert.SerializeObject(cacheKeys));

            await Console.Out.WriteLineAsync("���һ������");
            await _caching.SetStringAsync(cacheKey, "hi laozhang");
            await Console.Out.WriteLineAsync("ȫ��keys -->" + JsonConvert.SerializeObject(await _caching.GetAllCacheKeysAsync()));
            await Console.Out.WriteLineAsync("��ǰkey����-->" + JsonConvert.SerializeObject(await _caching.GetStringAsync(cacheKey)));

            await Console.Out.WriteLineAsync("ɾ��key");
            await _caching.RemoveAsync(cacheKey);
            await Console.Out.WriteLineAsync("ȫ��keys -->" + JsonConvert.SerializeObject(await _caching.GetAllCacheKeysAsync()));
        }
    }
}
