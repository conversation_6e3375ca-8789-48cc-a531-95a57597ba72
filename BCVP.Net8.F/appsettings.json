{
  "Logging": {
    "LogLevel": {
      "Default": "Information",
      "Microsoft.AspNetCore": "Warning"
    }
  },
  "AllowedHosts": "*",
  "Redis": {
    "Enable": true,
    "ConnectionString": "127.0.0.1:6379",
    "InstanceName": "" //前缀
  },
  //优化DB配置、不会再区分单库多库
  //MainDb：标识当前项目的主库，所对应的连接字符串的Enabled必须为true
  //Log:标识日志库，所对应的连接字符串的Enabled必须为true
  "MainDB": "Main", //当前项目的主库，所对应的连接字符串的Enabled必须为true
  "DBS": [
    /*
      对应下边的 DBType
      MySql = 0,
      SqlServer = 1,
      Sqlite = 2,
      Oracle = 3,
      PostgreSQL = 4,
      Dm = 5,//达梦
      Kdbndp = 6,//人大金仓
    */
    {
      "ConnId": "Main",
      "DBType": 2,
      "Enabled": true,
      "Connection": "WMBlog.db" //sqlite只写数据库名就行
    },
    {
      "ConnId": "Log", //日志库连接固定名称，不要改,其他的可以改
      "DBType": 2,
      "Enabled": true,
      "HitRate": 50,
      "Connection": "WMBlogLog.db" //sqlite只写数据库名就行
    }
  ]
}
