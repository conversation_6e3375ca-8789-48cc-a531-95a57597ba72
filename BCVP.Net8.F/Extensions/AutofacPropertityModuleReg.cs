using Autofac;
using Microsoft.AspNetCore.Mvc;

namespace BCVP.Net8.F.Extensions
{
    /// <summary>
    /// Autofac属性注入配置文件
    /// </summary>
    public class AutofacPropertityModuleReg : Module
    {
        protected override void Load(ContainerBuilder builder)
        {
            var controllerBaseType = typeof(ControllerBase);
            builder.RegisterAssemblyTypes(typeof(Program).Assembly)
                .Where(t => controllerBaseType.IsAssignableFrom(t) && t != controllerBaseType)
                .PropertiesAutowired();
        }
    }
}
