namespace BCVP.Net8.Common.Const
{
    /// <summary>
    /// 缓存相关常量
    /// </summary>
    public class CacheConst
    {
        /// <summary>
        /// 用户缓存
        /// </summary>
        public const string KeyUser = "user:";

        /// <summary>
        /// 用户部门缓存
        /// </summary>
        public const string KeyUserDepart = "userDepart:";

        /// <summary>
        /// 菜单缓存
        /// </summary>
        public const string KeyMenu = "menu:";

        /// <summary>
        /// 菜单
        /// </summary>
        public const string KeyPermissions = "permissions";

        /// <summary>
        /// 权限缓存
        /// </summary>
        public const string KeyPermission = "permission:";

        /// <summary>
        /// 接口路由
        /// </summary>
        public const string KeyModules = "modules";

        /// <summary>
        /// 系统配置
        /// </summary>
        public const string KeySystemConfig = "sysConfig";

        /// <summary>
        /// 查询过滤器缓存
        /// </summary>
        public const string KeyQueryFilter = "queryFilter:";

        /// <summary>
        /// 机构Id集合缓存
        /// </summary>
        public const string KeyOrgIdList = "org:";

        /// <summary>
        /// 最大角色数据范围缓存
        /// </summary>
        public const string KeyMaxDataScopeType = "maxDataScopeType:";

        /// <summary>
        /// 验证码缓存
        /// </summary>
        public const string KeyVerCode = "verCode:";

        /// <summary>
        /// 所有缓存关键字集合
        /// </summary>
        public const string KeyAll = "keys";

        /// <summary>
        /// 定时任务缓存
        /// </summary>
        public const string KeyTimer = "timer:";

        /// <summary>
        /// 在线用户缓存
        /// </summary>
        public const string KeyOnlineUser = "onlineuser:";

        /// <summary>
        /// 常量下拉框
        /// </summary>
        public const string KeyConstSelector = "selector:";

        /// <summary>
        /// swagger登录缓存
        /// </summary>
        public const string SwaggerLogin = "swaggerLogin:";
    }
}