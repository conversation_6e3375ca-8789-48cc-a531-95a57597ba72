<Project Sdk="Microsoft.NET.Sdk">

  <PropertyGroup>
    <TargetFramework>net8.0</TargetFramework>
    <ImplicitUsings>enable</ImplicitUsings>
    <Nullable>enable</Nullable>
  </PropertyGroup>

  <ItemGroup>
    <PackageReference Include="AutoMapper" Version="13.0.1" />
    <PackageReference Include="Newtonsoft.Json" Version="13.0.3" />
	<PackageReference Include="Microsoft.Extensions.Configuration.Binder" Version="8.0.0" />
	<PackageReference Include="Microsoft.Extensions.Configuration.Json" Version="8.0.0" />
	<PackageReference Include="Microsoft.Extensions.DependencyModel" Version="8.0.0" />
	<PackageReference Include="Microsoft.Extensions.Options.ConfigurationExtensions" Version="8.0.0" />

	 <PackageReference Include="Serilog" Version="3.1.1" />
	 <PackageReference Include="Serilog.AspNetCore" Version="8.0.0" />
  </ItemGroup>

  <ItemGroup>
    <ProjectReference Include="..\BCVP.Net8.Model\BCVP.Net8.Model.csproj" />
  </ItemGroup>

</Project>
