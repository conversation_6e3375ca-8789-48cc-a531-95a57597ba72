using BCVP.Net8.Model;
using Newtonsoft.Json;
using SqlSugar;
using System.Text.Json.Serialization;

namespace BCVP.Net8.Repository.Base
{
    public class BaseRepository<TEntity> : IBaseRepository<TEntity> where TEntity : class, new()
    {
        private readonly ISqlSugarClient _sqlSugarClient;

        public BaseRepository(ISqlSugarClient sqlSugarClient)
        {
            _sqlSugarClient = sqlSugarClient;
        }

        public ISqlSugarClient Db => _sqlSugarClient;

        //public ISqlSugarClient db
        //{
        //    get { return _sqlSugarClient; }
        //}

        public async Task<List<TEntity>> Query()
        {
            //await Task.CompletedTask;
            //var data = "[{\"Id\":18,\"Name\":\"18Name\"}]";
            //return JsonConvert.DeserializeObject<List<TEntity>>(data) ?? new List<TEntity>();

            return await _sqlSugarClient.Queryable<TEntity>().ToListAsync();

        }

        /// <summary>
        /// 写入实体数据
        /// </summary>
        /// <param name="entity">博文实体类</param>
        /// <returns></returns>
        public async Task<long> Add(TEntity entity)
        {
            var insert = _sqlSugarClient.Insertable(entity);
            return await insert.ExecuteReturnSnowflakeIdAsync();
        }
    }
}
