using BCVP.Net8.Model;

namespace BCVP.Net8.Repository
{
    /// <summary>
    /// 仓储层：控制DB对象，负责对数据库的连接，控制，多库切换，事务，不负责业务，业务放在Service层
    /// </summary>
    public class UserRepository : IUserRepository
    {
        public async Task<List<SysUserInfo>> Query()
        {
            await Task.CompletedTask;
            var data = new List<SysUserInfo>()
            {
                new SysUserInfo(){
                Id = 1,
                Name = "Test",
                }
            };

            return data;
        }
    }
}
