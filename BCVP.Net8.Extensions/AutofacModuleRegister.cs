using Autofac;
using Autofac.Extras.DynamicProxy;
using BCVP.Net8.IService;
using BCVP.Net8.Repository.Base;
using BCVP.Net8.Repository.UnitOfWorks;
using BCVP.Net8.Service;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Reflection;
using System.Text;
using System.Threading.Tasks;

namespace BCVP.Net8.Extensions
{
    public class AutofacModuleRegister : Autofac.Module
    {
        protected override void Load(ContainerBuilder builder)
        {
            var basePath = AppContext.BaseDirectory;

            //获取程序集路径
            var servicesDllFile = Path.Combine(basePath, "BCVP.Net8.Service.dll");
            var repositoryDllFile = Path.Combine(basePath, "BCVP.Net8.Repository.dll");

            //注册AOP
            var aopTypes = new List<Type>() { typeof(ServiceAOP) };
            builder.RegisterType<ServiceAOP>();

            //注册仓储
            builder.RegisterGeneric(typeof(BaseRepository<>)).As(typeof(IBaseRepository<>)).InstancePerDependency();

            //注册服务
            builder.RegisterGeneric(typeof(BaseService<,>)).As(typeof(IBaseService<,>))
                .InstancePerDependency()
                .EnableInterfaceInterceptors()//将AOP挂载到Service程序集里面;
                .InterceptedBy(aopTypes.ToArray());

            // 获取 Service.dll 程序集服务，并注册
            var assemblysServices = Assembly.LoadFrom(servicesDllFile);
            builder.RegisterAssemblyTypes(assemblysServices)
                .AsImplementedInterfaces()//
                .InstancePerDependency()//瞬态模式
                .PropertiesAutowired()//属性注入
                .EnableInterfaceInterceptors()
                .InterceptedBy(aopTypes.ToArray());//将AOP挂载到Service程序集里面

            // 获取 Repository.dll 程序集服务，并注册
            var assemblysRepository = Assembly.LoadFrom(repositoryDllFile);
            builder.RegisterAssemblyTypes(assemblysRepository)
                .AsImplementedInterfaces()
                .PropertiesAutowired()
                .InstancePerDependency();

            builder.RegisterType<UnitOfWorkManage>().As<IUnitOfWorkManage>()
             .AsImplementedInterfaces()
             .InstancePerLifetimeScope()
             .PropertiesAutowired();
        }
    }
}
