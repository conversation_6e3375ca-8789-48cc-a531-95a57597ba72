using AutoMapper;
using BCVP.Net8.Model;
using BCVP.Net8.Model.Vo;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace BCVP.Net8.Extensions
{
    public class CustomProfile : Profile
    {
        public CustomProfile()
        {
            CreateMap<Role, RoleVo>().ForMember(a => a.RoleName, o => o.MapFrom(d => d.Name));
            CreateMap<RoleVo, Role>().ForMember(a => a.Name, o => o.MapFrom(d => d.RoleName));
            CreateMap<SysUserInfo, UserVo>()
                .ForMember(a => a.UserName, o => o.MapFrom(d => d.Name));
            CreateMap<UserVo, SysUserInfo>()
                .ForMember(a => a.Name, o => o.MapFrom(d => d.UserName));
        }
    }
}
