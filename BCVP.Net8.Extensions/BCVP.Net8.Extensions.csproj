<Project Sdk="Microsoft.NET.Sdk">

  <PropertyGroup>
    <TargetFramework>net8.0</TargetFramework>
    <ImplicitUsings>enable</ImplicitUsings>
    <Nullable>enable</Nullable>
  </PropertyGroup>

  <ItemGroup>
    <PackageReference Include="Autofac.Extensions.DependencyInjection" Version="9.0.0" />
    <PackageReference Include="Autofac.Extras.DynamicProxy" Version="7.1.0" />

	<PackageReference Include="Microsoft.Extensions.Caching.StackExchangeRedis" Version="8.0.0" />
	<PackageReference Include="StackExchange.Redis" Version="2.7.10" />
  </ItemGroup>

  <ItemGroup>
    <ProjectReference Include="..\BCVP.Net8.Common\BCVP.Net8.Common.csproj" />
    <ProjectReference Include="..\BCVP.Net8.Repository\BCVP.Net8.Repository.csproj" />
    <ProjectReference Include="..\BCVP.Net8.Service\BCVP.Net8.Service.csproj" />
  </ItemGroup>

</Project>
